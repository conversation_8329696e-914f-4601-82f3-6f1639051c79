# ShortLinker WordPress Plugin

A WordPress plugin that generates and manages unique 4-character short links for posts using the eanlibya.news domain.

## Features

- **Auto-Generation**: Automatically generates shortlinks when posts are published
- **Bulk Generation**: Admin tool to generate shortlinks for all existing published posts
- **Meta Box**: Shows shortlink in post editor with copy functionality
- **Frontend Shortcode**: `[copy_shortlink]` shortcode for copy buttons on posts
- **External Sync**: Syncs shortlinks with eanlibya.news redirector API
- **Unique Codes**: Generates unique 4-character alphanumeric codes

## Installation

1. Upload the `shortlinker` folder to your `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Go to Settings > ShortLinker to configure the API settings
4. Set your API secret key (must match the one in eanlibya.news/api/add.php)

## Configuration

### WordPress Plugin Settings
1. Go to **Settings > ShortLinker**
2. Set the **API Secret Key** (must match the redirector)
3. Verify the **Redirector API Endpoint** URL

### Redirector Setup (eanlibya.news)
1. Upload the redirector files to your eanlibya.news domain
2. Edit `api/add.php` and change the `$API_SECRET` value
3. Ensure the redirects.json file is writable by PHP
4. Configure your web server to serve the redirector files

## Usage

### Automatic Shortlink Generation
- Shortlinks are automatically generated when posts are published
- No manual intervention required for new posts

### Bulk Generation
1. Go to **Tools > ShortLinker**
2. Click "Bulk Generate Shortlinks" to create shortlinks for existing posts

### Frontend Copy Button
Add the shortcode to your post content or theme:
```
[copy_shortlink]
```

### Meta Box
- View and copy shortlinks directly from the post editor
- Located in the sidebar of post/page edit screens

## Technical Details

### Storage
- Shortlinks are stored in post meta with key `_short_code`
- 4-character alphanumeric format: `[a-zA-Z0-9]`
- Collision detection ensures uniqueness

### API Integration
- Syncs with eanlibya.news redirector via POST requests
- Uses `X-Shortlink-Key` header for authentication
- Automatic retry logic for failed syncs

### Hooks Used
- `save_post`: Auto-generate shortlinks
- `add_meta_boxes`: Post editor meta box
- `admin_menu`: Admin pages
- `add_shortcode`: Frontend shortcode

## File Structure
```
shortlinker/
├── shortlinker.php     # Main plugin file
├── js/
│   ├── admin.js        # Admin copy functionality
│   └── frontend.js     # Frontend copy functionality
└── README.md           # This file
```

## Requirements
- WordPress 5.0+
- PHP 7.0+
- eanlibya.news redirector setup

## Security
- API authentication via shared secret
- Input validation and sanitization
- WordPress nonces for admin actions
- Secure clipboard operations

## Support
For issues or questions, please contact the development team.
