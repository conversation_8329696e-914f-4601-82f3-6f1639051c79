
short.ly Redirector

This is a minimal PHP redirector for 4-character short codes.

Structure:
- index.php : handles incoming short URLs and redirects.
- redirects.json : stores mappings of codes to URLs.
- api/add.php : secure API to add/update mappings.

Security:
- The API endpoint requires a header 'X-Shortlink-Key' with the secret token.
- Change the $API_SECRET value in api/add.php before deploying.

Usage:
- Point DNS for short.ly to your server.
- Configure your webserver virtual host to serve this folder.
- Use WordPress plugin to generate short codes and POST to api/add.php with authentication.

Example API POST request header:
  X-Shortlink-Key: your-secret-token

Ensure file permissions allow <PERSON><PERSON> to read/write redirects.json.

