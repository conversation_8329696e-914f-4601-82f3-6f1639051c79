jQuery(document).ready(function($) {
    // Copy shortlink functionality for frontend
    $('.shortlinker-copy-btn').on('click', function(e) {
        e.preventDefault();
        
        var url = $(this).data('url');
        var button = $(this);
        var originalContent = button.html(); // Use html() to preserve icons
        
        // Try to copy to clipboard
        if (navigator.clipboard && window.isSecureContext) {
            // Modern clipboard API
            navigator.clipboard.writeText(url).then(function() {
                button.html('تم النسخ!');
                button.addClass('copied');
                setTimeout(function() {
                    button.html(originalContent);
                    button.removeClass('copied');
                }, 2000);
            }).catch(function(err) {
                console.error('Failed to copy: ', err);
                fallbackCopyTextToClipboard(url, button, originalContent);
            });
        } else {
            // Fallback for older browsers
            fallbackCopyTextToClipboard(url, button, originalContent);
        }
    });
    
    function fallbackCopyTextToClipboard(text, button, originalContent) {
        var textArea = document.createElement("textarea");
        textArea.value = text;
        
        // Avoid scrolling to bottom
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            var successful = document.execCommand('copy');
            if (successful) {
                button.html('تم النسخ!');
                button.addClass('copied');
                setTimeout(function() {
                    button.html(originalContent);
                    button.removeClass('copied');
                }, 2000);
            } else {
                button.html('فشل النسخ');
                setTimeout(function() {
                    button.html(originalContent);
                }, 2000);
            }
        } catch (err) {
            console.error('Fallback: Oops, unable to copy', err);
            button.html('Copy failed');
            setTimeout(function() {
                button.html(originalContent);
            }, 2000);
        }
        
        document.body.removeChild(textArea);
    }
});

// Add some basic styling for the copy button
jQuery(document).ready(function($) {
    // Add CSS for the copy button
    var css = `
        .shortlinker-copy-btn {
            background-color: #0073aa;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }
        .shortlinker-copy-btn:hover {
            background-color: #005a87;
        }
        .shortlinker-copy-btn.copied {
            background-color: #46b450;
        }
    `;
    
    $('<style>').prop('type', 'text/css').html(css).appendTo('head');
});
