# ShortLinker Installation Guide

This guide will help you set up the ShortLinker WordPress plugin and the eanlibya.news redirector.

## Prerequisites

- WordPress website (eanlibya.com)
- Access to eanlibya.news domain and hosting
- PHP 7.0+ on both servers
- FTP/SFTP access to upload files

## Part 1: Set Up the Redirector (eanlibya.news)

### 1. Upload Redirector Files
Upload the contents of the `eanlibya-redirector` folder to your eanlibya.news domain root:

```
eanlibya.news/
├── index.php
├── redirects.json
├── api/
│   └── add.php
└── README.txt
```

### 2. Configure the API Secret
1. Edit `api/add.php` on eanlibya.news
2. Change line 19: `$API_SECRET = 'change-me-to-a-secure-random-string';`
3. Replace with a strong, unique secret key (save this for later)

Example:
```php
$API_SECRET = 'el_live_a8f7d9e2b1c4f6h8j9k2m5n7p9q1r3s5';
```

### 3. Set File Permissions
Ensure `redirects.json` is writable by PHP:
```bash
chmod 644 redirects.json
```

### 4. Test the Redirector
1. Visit `https://eanlibya.news/` - should show "Not Found" (expected)
2. Check that the API endpoint exists: `https://eanlibya.news/api/add.php`

## Part 2: Install WordPress Plugin

### 1. Upload Plugin Files
Upload the `shortlinker` folder to your WordPress plugins directory:

```
wp-content/plugins/shortlinker/
├── shortlinker.php
├── js/
│   ├── admin.js
│   └── frontend.js
├── css/
│   └── admin.css
└── README.md
```

### 2. Activate the Plugin
1. Go to WordPress Admin → Plugins
2. Find "ShortLinker" and click "Activate"

### 3. Configure Plugin Settings
1. Go to **Settings → ShortLinker**
2. Enter the **API Secret Key** (same as step 2 above)
3. Verify the **Redirector API Endpoint**: `https://eanlibya.news/api/add.php`
4. Click "Save Changes"

## Part 3: Generate Shortlinks

### Option 1: Bulk Generate (Existing Posts)
1. Go to **Tools → ShortLinker**
2. Click "Bulk Generate Shortlinks"
3. Wait for completion message

### Option 2: Automatic (New Posts)
- Shortlinks are automatically generated when you publish new posts
- No manual action required

## Part 4: Using Shortlinks

### In Post Editor
- Edit any post/page
- Look for the "ShortLink" meta box in the sidebar
- Copy the shortlink using the "Copy Link" button

### Frontend Copy Button
Add this shortcode to your post content:
```
[copy_shortlink]
```

### Manual Access
- Original: `https://www.eanlibya.com/my-post-title`
- Shortlink: `https://eanlibya.news/a3Bx` (example)

## Troubleshooting

### Common Issues

**1. "Forbidden" Error When Generating Shortlinks**
- Check that API secret keys match in both plugin settings and `api/add.php`
- Verify the API endpoint URL is correct

**2. Shortlinks Not Working**
- Check that `redirects.json` exists and is writable
- Verify DNS is pointing correctly to eanlibya.news
- Check web server configuration

**3. "Service Error" on eanlibya.news**
- Ensure `redirects.json` file exists
- Check file permissions (should be 644)
- Verify PHP can read/write the file

### Testing the Setup

1. **Test API Connection:**
   ```bash
   curl -X POST https://eanlibya.news/api/add.php \
     -H "Content-Type: application/json" \
     -H "X-Shortlink-Key: your-secret-key" \
     -d '{"code":"test","url":"https://www.eanlibya.com"}'
   ```

2. **Test Redirect:**
   - Visit `https://eanlibya.news/test`
   - Should redirect to `https://www.eanlibya.com`

3. **Check WordPress Integration:**
   - Create a new post and publish it
   - Check the ShortLink meta box for the generated shortlink
   - Test the shortlink in a browser

## Security Notes

- Keep your API secret key secure and unique
- Consider enabling IP whitelisting in `api/add.php` if needed
- Regularly backup your `redirects.json` file
- Monitor server logs for suspicious activity

## Support

If you encounter issues:
1. Check WordPress and server error logs
2. Verify all file permissions are correct
3. Test the API connection manually
4. Ensure both domains are accessible

For additional help, contact the development team.
