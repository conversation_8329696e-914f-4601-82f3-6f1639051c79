<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, X-Shortlink-Key');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Simple shared secret for authentication
$API_SECRET = 'el_live_a8f7d9e2b1c4f6h8j9k2m5n7p9q1r3s5';

// Check for auth header with better error handling
$headers = getallheaders();
if (!$headers) {
    $headers = [];
    // Fallback for servers that don't support getallheaders()
    foreach ($_SERVER as $key => $value) {
        if (strpos($key, 'HTTP_') === 0) {
            $header = str_replace('_', '-', substr($key, 5));
            $headers[$header] = $value;
        }
    }
}

if (!isset($headers['X-Shortlink-Key']) || $headers['X-Shortlink-Key'] !== $API_SECRET) {
    http_response_code(403);
    echo json_encode(['error' => 'Forbidden', 'message' => 'Invalid or missing API key']);
    exit;
}

// Optional IP whitelist (commented out, enable if needed)
// $allowed_ips = ['***************']; // Replace with your WP server IP
// if (!in_array($_SERVER['REMOTE_ADDR'], $allowed_ips)) {
//     http_response_code(403);
//     echo json_encode(['error' => 'IP not allowed']);
//     exit;
// }

// Parse JSON input with error handling
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON', 'message' => json_last_error_msg()]);
    exit;
}

$code = $data['code'] ?? '';
$url = $data['url'] ?? '';

// Validate input
if (!preg_match('/^[a-zA-Z0-9]{4}$/', $code)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid code format', 'message' => 'Code must be exactly 4 alphanumeric characters']);
    exit;
}

if (!filter_var($url, FILTER_VALIDATE_URL)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid URL', 'message' => 'URL must be a valid HTTP/HTTPS URL']);
    exit;
}

// Load existing redirects with error handling
$path = __DIR__ . '/../redirects.json';
$map = [];

if (file_exists($path)) {
    $existing_data = file_get_contents($path);
    $map = json_decode($existing_data, true);

    if ($map === null) {
        // Backup corrupted file
        $backup_path = $path . '.backup.' . date('Y-m-d-H-i-s');
        file_put_contents($backup_path, $existing_data);
        $map = [];
    }
}

// Add/update the mapping
$map[$code] = $url;

// Save with error handling
$json_data = json_encode($map, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
if ($json_data === false) {
    http_response_code(500);
    echo json_encode(['error' => 'JSON encoding failed']);
    exit;
}

if (file_put_contents($path, $json_data) === false) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to save redirects']);
    exit;
}

// Log successful addition (optional - uncomment if needed)
// error_log("Shortlink added: $code -> $url");

echo json_encode(['success' => true, 'code' => $code, 'url' => $url]);
