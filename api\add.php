<?php
header('Content-Type: application/json');

// Simple shared secret for authentication
$API_SECRET = 'change-me-to-a-secure-random-string';

// Check for auth header
$headers = getallheaders();
if (!isset($headers['X-Shortlink-Key']) || $headers['X-Shortlink-Key'] !== $API_SECRET) {
    http_response_code(403);
    echo json_encode(['error' => 'Forbidden']);
    exit;
}

// Optional IP whitelist (commented out, enable if needed)
// $allowed_ips = ['***************']; // Replace with your WP server IP
// if (!in_array($_SERVER['REMOTE_ADDR'], $allowed_ips)) {
//     http_response_code(403);
//     echo json_encode(['error' => 'IP not allowed']);
//     exit;
// }

$data = json_decode(file_get_contents('php://input'), true);
$code = $data['code'] ?? '';
$url = $data['url'] ?? '';

if (!preg_match('/^[a-zA-Z0-9]{4}$/', $code) || !filter_var($url, FILTER_VALIDATE_URL)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid input']);
    exit;
}

$path = __DIR__ . '/../redirects.json';
$map = file_exists($path) ? json_decode(file_get_contents($path), true) : [];

$map[$code] = $url;
file_put_contents($path, json_encode($map, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));

echo json_encode(['success' => true]);
