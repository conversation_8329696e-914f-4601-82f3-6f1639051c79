jQuery(document).ready(function($) {
    // Copy shortlink functionality for admin
    $('.shortlinker-copy-btn').on('click', function(e) {
        e.preventDefault();
        
        var url = $(this).data('url');
        var button = $(this);
        var originalText = button.text();
        
        // Try to copy to clipboard
        if (navigator.clipboard && window.isSecureContext) {
            // Modern clipboard API
            navigator.clipboard.writeText(url).then(function() {
                button.text('Copied!');
                setTimeout(function() {
                    button.text(originalText);
                }, 2000);
            }).catch(function(err) {
                console.error('Failed to copy: ', err);
                fallbackCopyTextToClipboard(url, button, originalText);
            });
        } else {
            // Fallback for older browsers
            fallbackCopyTextToClipboard(url, button, originalText);
        }
    });
    
    function fallbackCopyTextToClipboard(text, button, originalText) {
        var textArea = document.createElement("textarea");
        textArea.value = text;
        
        // Avoid scrolling to bottom
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            var successful = document.execCommand('copy');
            if (successful) {
                button.text('Copied!');
                setTimeout(function() {
                    button.text(originalText);
                }, 2000);
            } else {
                button.text('Copy failed');
                setTimeout(function() {
                    button.text(originalText);
                }, 2000);
            }
        } catch (err) {
            console.error('Fallback: Oops, unable to copy', err);
            button.text('Copy failed');
            setTimeout(function() {
                button.text(originalText);
            }, 2000);
        }
        
        document.body.removeChild(textArea);
    }
});
