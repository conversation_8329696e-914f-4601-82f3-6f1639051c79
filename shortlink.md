# ShortLinker – WordPress Plugin PRD

## Plugin Name
**ShortLinker** – Custom WordPress Shortlink Manager

## Purpose
To generate and manage unique 4-character short links for WordPress posts using a custom short domain and provide a way for users to copy/share those shortlinks easily.

---

## Key Features

### 1. Bulk Generation of Short Links
- Scan all published posts.
- Generate a unique 4-character alphanumeric code for posts missing shortlinks.
- Save code in a custom post meta field.
- Optionally sync with external redirector mapping.

### 2. Auto-Generate on Post Publish
- Hook into post publishing process.
- If a shortcode does not exist for the post, generate and store one.
- Ensure shortcodes are unique.

### 3. Shortlink Redirection (eanlibya.news)
- All shortlinks use a 4-character path, e.g. `https://eanlibya.news/x9d3`
- `eanlibya.news` is a minimal PHP app with:
  - `index.php`: handles lookups and redirects
  - `redirects.json`: stores code → URL mappings
  - `api/add.php`: REST endpoint for WordPress to push new mappings
- WordPress plugin sends shortlink mappings to the redirector via POST requests
- Fast, independent of WordPress availability, suitable for large-scale usage

### 4. Shortcode for "Copy Link" Button
- `[copy_shortlink]` shortcode.
- Outputs a styled copy-to-clipboard button with the post’s shortlink.
- Only works on single post pages.

### 5. Admin UI
- Meta box in post editor showing:
  - Shortlink (read-only).
  - "Copy" button.
- Admin Tools page:
  - Button to bulk generate missing shortlinks.
  - Shows generation results (number of links created).

## Security for External Redirector (eanlibya.news)

The `eanlibya.news` redirector site exposes a POST endpoint at `/api/add` which WordPress uses to sync shortlink mappings. This endpoint is protected using the following measures:

### Header-Based Authentication
- WordPress sends a custom header `X-Shortlink-Key` with a shared secret token.
- The redirector rejects any request that does not provide this key or provides an incorrect one.

### (Optional) IP Whitelisting
- The redirector may additionally restrict access to known server IPs (i.e., the IP of the WordPress host).
- This is an optional but recommended second layer of protection.

### Token Rotation
- The secret token should be stored in both the plugin settings and the redirector.
- In case of a breach or rotation, both systems must be updated.

### Security Implementation Location
- All security logic is inside `eanlibya.news/api/add.php`.
- The redirector does not expose any write operations publicly without authentication.


---

## Technical Details

### Storage
- `post_meta`: Key `_short_code`
- Value: 4-character alphanumeric string
- Optional: plugin-managed custom database table for external redirector mapping

### Code Format
- Length: 4 characters
- Characters: `[a-zA-Z0-9]`
- Must be unique across all posts

### Hooks & Components
- `save_post`: Auto-generate shortcodes
- `add_meta_box`: Show shortlink meta box
- `admin_menu`: Add bulk generator tools page
- `add_shortcode`: Register `[copy_shortlink]`

---

## Deliverables

- `/shortlinker/shortlinker.php`: Main plugin file
- `/shortlinker/js/copy.js`: Clipboard logic
- Admin Tools page (`Tools > ShortLinker`)
- Shortlink generator logic
- Meta box UI
- Shortcode for front-end copy button

---

## Acceptance Criteria

| Feature              | Criteria                                                                 |
|----------------------|--------------------------------------------------------------------------|
| Bulk Generation       | Button scans published posts and fills in missing shortlinks            |
| Auto Generation       | New posts receive shortlink on publish                                  |
| Unique Code Format    | Generated codes are 4 characters and globally unique                    |
| Copy Button Shortcode | `[copy_shortlink]` outputs a button that copies the full short URL      |
| Meta Box              | Appears in post editor; shows shortlink and allows copy                 |
| Redirector            | External domain correctly maps shortcodes to original URLs              |

---

## Development Workflow

### 1. Setup
- Create plugin folder: `wp-content/plugins/shortlinker`
- Add plugin header and initialize plugin hooks

### 2. Code Generation
- Create `generate_unique_code()` method
- Store shortcode in `_short_code` meta
- Add collision check using existing post meta

### 3. Auto-Generation
- Hook into `save_post`
- Check if shortcode exists; generate and save if missing

### 4. Bulk Generator
- Admin page under `Tools`
- Button runs a scan for all published posts without shortcodes
- Displays a success message and stats

### 5. Copy Shortcode Button
- Register `[copy_shortlink]`
- Output HTML with JS-powered copy-to-clipboard
- Load JS only on single post pages

### 6. Post Meta Box
- Use `add_meta_box()` to insert UI
- Show existing shortlink
- Read-only input + Copy button

---

## Notes
- Redirector logic is assumed to be hosted on the short domain (`eanlibya.news`) as a separate system.


