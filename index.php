<?php
// Enhanced redirector with better error handling and logging

// Get the URI path
$uri = trim($_SERVER['REQUEST_URI'], '/');

// Handle root domain redirect
if (empty($uri)) {
    header("Location: https://www.eanlibya.com", true, 301);
    exit;
}

// Validate 4-character alphanumeric code
if (!preg_match('/^[a-zA-Z0-9]{4}$/', $uri)) {
    header("Location: https://www.eanlibya.com/404", true, 301);
    exit;
}

// Load redirects with error handling
$redirects_file = __DIR__ . '/redirects.json';
if (!file_exists($redirects_file)) {
    http_response_code(500);
    header('Content-Type: text/html; charset=UTF-8');
    echo '<!DOCTYPE html><html><head><title>Service Error</title></head><body><h1>500 - Service Error</h1><p>Redirects database not found.</p></body></html>';
    exit;
}

$map = json_decode(file_get_contents($redirects_file), true);
if ($map === null) {
    http_response_code(500);
    header('Content-Type: text/html; charset=UTF-8');
    echo '<!DOCTYPE html><html><head><title>Service Error</title></head><body><h1>500 - Service Error</h1><p>Redirects database corrupted.</p></body></html>';
    exit;
}

// Perform redirect or show 404
if (isset($map[$uri])) {
    // Log successful redirect (optional - uncomment if needed)
    // error_log("Shortlink redirect: $uri -> " . $map[$uri]);

    header("Location: " . $map[$uri], true, 301);
    exit;
} else {
    header("Location: https://www.eanlibya.com/404", true, 301);
    exit;
}
