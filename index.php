<?php
// Enhanced redirector with Facebook mobile app support

// Get the URI path
$uri = trim($_SERVER['REQUEST_URI'], '/');

// Detect Facebook mobile app
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
$is_facebook_app = (
    strpos($user_agent, 'FBAN') !== false ||
    strpos($user_agent, 'FBAV') !== false ||
    strpos($user_agent, 'Facebook') !== false
);

// Handle root domain redirect
if (empty($uri)) {
    if ($is_facebook_app) {
        // Use JavaScript redirect for Facebook app
        echo '<!DOCTYPE html>
<html>
<head>
    <title>Redirecting...</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <script>
        window.location.href = "https://www.eanlibya.com";
    </script>
    <noscript>
        <meta http-equiv="refresh" content="0;url=https://www.eanlibya.com">
        <p><a href="https://www.eanlibya.com">Click here if you are not redirected automatically</a></p>
    </noscript>
</body>
</html>';
    } else {
        header("Location: https://www.eanlibya.com", true, 301);
    }
    exit;
}

// Validate 4-character alphanumeric code
if (!preg_match('/^[a-zA-Z0-9]{4}$/', $uri)) {
    header("Location: https://www.eanlibya.com/404", true, 301);
    exit;
}

// Load redirects with error handling
$redirects_file = __DIR__ . '/redirects.json';
if (!file_exists($redirects_file)) {
    http_response_code(500);
    header('Content-Type: text/html; charset=UTF-8');
    echo '<!DOCTYPE html><html><head><title>Service Error</title></head><body><h1>500 - Service Error</h1><p>Redirects database not found.</p></body></html>';
    exit;
}

$map = json_decode(file_get_contents($redirects_file), true);
if ($map === null) {
    http_response_code(500);
    header('Content-Type: text/html; charset=UTF-8');
    echo '<!DOCTYPE html><html><head><title>Service Error</title></head><body><h1>500 - Service Error</h1><p>Redirects database corrupted.</p></body></html>';
    exit;
}

// Perform redirect or show 404
if (isset($map[$uri])) {
    $target_url = $map[$uri];

    // Log successful redirect (optional - uncomment if needed)
    // error_log("Shortlink redirect: $uri -> $target_url (User-Agent: $user_agent)");

    if ($is_facebook_app) {
        // Use JavaScript redirect for Facebook app with fallbacks
        echo '<!DOCTYPE html>
<html>
<head>
    <title>Redirecting...</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:url" content="' . htmlspecialchars($target_url) . '">
    <meta property="og:type" content="website">
    <link rel="canonical" href="' . htmlspecialchars($target_url) . '">
</head>
<body>
    <script>
        // Multiple redirect methods for Facebook app
        setTimeout(function() {
            window.location.href = "' . addslashes($target_url) . '";
        }, 100);

        // Immediate redirect attempt
        window.location.replace("' . addslashes($target_url) . '");

        // Fallback
        if (window.location.href.indexOf("' . addslashes($target_url) . '") === -1) {
            window.open("' . addslashes($target_url) . '", "_top");
        }
    </script>
    <noscript>
        <meta http-equiv="refresh" content="0;url=' . htmlspecialchars($target_url) . '">
        <p><a href="' . htmlspecialchars($target_url) . '">Click here if you are not redirected automatically</a></p>
    </noscript>
    <div style="text-align: center; padding: 20px; font-family: Arial, sans-serif;">
        <p>Redirecting you to the article...</p>
        <p><a href="' . htmlspecialchars($target_url) . '" style="color: #1877f2; text-decoration: none;">Click here if you are not redirected automatically</a></p>
    </div>
</body>
</html>';
    } else {
        // Standard HTTP redirect for regular browsers
        header("Location: " . $target_url, true, 301);
    }
    exit;
} else {
    // Handle 404 - shortlink not found
    if ($is_facebook_app) {
        // JavaScript redirect to 404 page for Facebook app
        echo '<!DOCTYPE html>
<html>
<head>
    <title>Page Not Found</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <script>
        window.location.href = "https://www.eanlibya.com/404";
    </script>
    <noscript>
        <meta http-equiv="refresh" content="0;url=https://www.eanlibya.com/404">
        <p><a href="https://www.eanlibya.com/404">Click here if you are not redirected automatically</a></p>
    </noscript>
</body>
</html>';
    } else {
        header("Location: https://www.eanlibya.com/404", true, 301);
    }
    exit;
}
