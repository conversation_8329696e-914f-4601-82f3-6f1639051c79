<?php
/**
 * Plugin Name: ShortLinker
 * Plugin URI: https://eanlibya.com
 * Description: Custom WordPress Shortlink Manager - Generate and manage unique 4-character short links for WordPress posts using eanlibya.news domain.
 * Version: 1.0.0
 * Author: EanLibya
 * License: GPL v2 or later
 * Text Domain: shortlinker
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SHORTLINKER_VERSION', '1.0.0');
define('SHORTLINKER_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('SHORTLINKER_PLUGIN_URL', plugin_dir_url(__FILE__));

class ShortLinker {
    
    private $short_domain = 'https://eanlibya.news';
    private $api_endpoint = 'https://eanlibya.news/api/add.php';
    private $meta_key = '_short_code';
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('save_post', array($this, 'auto_generate_shortlink'), 10, 2);
        add_action('add_meta_boxes', array($this, 'add_shortlink_meta_box'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        add_shortcode('copy_shortlink', array($this, 'copy_shortlink_shortcode'));
        
        // Add settings
        add_action('admin_init', array($this, 'register_settings'));
    }
    
    public function init() {
        // Plugin initialization
    }
    
    /**
     * Generate a unique 4-character alphanumeric code
     */
    public function generate_unique_code() {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $max_attempts = 1000;
        $attempts = 0;
        
        do {
            $code = '';
            for ($i = 0; $i < 4; $i++) {
                $code .= $characters[rand(0, strlen($characters) - 1)];
            }
            $attempts++;
            
            // Check if code already exists
            $existing = get_posts(array(
                'post_type' => 'any',
                'post_status' => 'any',
                'meta_key' => $this->meta_key,
                'meta_value' => $code,
                'fields' => 'ids',
                'posts_per_page' => 1
            ));
            
        } while (!empty($existing) && $attempts < $max_attempts);
        
        if ($attempts >= $max_attempts) {
            return false; // Could not generate unique code
        }
        
        return $code;
    }
    
    /**
     * Auto-generate shortlink on post publish
     */
    public function auto_generate_shortlink($post_id, $post) {
        // Only for published posts
        if ($post->post_status !== 'publish') {
            return;
        }
        
        // Skip if shortlink already exists
        if (get_post_meta($post_id, $this->meta_key, true)) {
            return;
        }
        
        // Generate and save shortlink
        $code = $this->generate_unique_code();
        if ($code) {
            update_post_meta($post_id, $this->meta_key, $code);
            $this->sync_with_redirector($code, get_permalink($post_id));
        }
    }
    
    /**
     * Add shortlink meta box to post editor
     */
    public function add_shortlink_meta_box() {
        add_meta_box(
            'shortlinker_meta_box',
            'ShortLink',
            array($this, 'shortlink_meta_box_callback'),
            array('post', 'page'),
            'side',
            'default'
        );
    }
    
    /**
     * Meta box callback
     */
    public function shortlink_meta_box_callback($post) {
        $short_code = get_post_meta($post->ID, $this->meta_key, true);
        $short_url = $short_code ? $this->short_domain . '/' . $short_code : '';
        
        wp_nonce_field('shortlinker_meta_box', 'shortlinker_meta_box_nonce');
        
        echo '<p><label for="shortlinker_url">Short URL:</label></p>';
        if ($short_url) {
            echo '<input type="text" id="shortlinker_url" value="' . esc_attr($short_url) . '" readonly style="width: 100%;" />';
            echo '<p><button type="button" class="button shortlinker-copy-btn" data-url="' . esc_attr($short_url) . '">Copy Link</button></p>';
        } else {
            echo '<p><em>Shortlink will be generated when post is published.</em></p>';
        }
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_management_page(
            'ShortLinker Tools',
            'ShortLinker',
            'manage_options',
            'shortlinker-tools',
            array($this, 'admin_tools_page')
        );
        
        add_options_page(
            'ShortLinker Settings',
            'ShortLinker',
            'manage_options',
            'shortlinker-settings',
            array($this, 'settings_page')
        );
    }
    
    /**
     * Admin tools page
     */
    public function admin_tools_page() {
        if (isset($_POST['bulk_generate']) && wp_verify_nonce($_POST['shortlinker_nonce'], 'bulk_generate')) {
            // Increase execution time and memory for bulk operations
            @ini_set('max_execution_time', 300); // 5 minutes
            @ini_set('memory_limit', '256M');

            try {
                $results = $this->bulk_generate_shortlinks();
                if ($results['error']) {
                    echo '<div class="notice notice-error"><p><strong>Error:</strong> ' . esc_html($results['error']) . '</p></div>';
                } else {
                    echo '<div class="notice notice-success"><p><strong>Success!</strong> Generated ' . $results['generated'] . ' shortlinks. Skipped ' . $results['skipped'] . ' posts. ' . ($results['errors'] > 0 ? 'Encountered ' . $results['errors'] . ' errors.' : '') . '</p></div>';
                }
            } catch (Exception $e) {
                echo '<div class="notice notice-error"><p><strong>Critical Error:</strong> ' . esc_html($e->getMessage()) . '</p></div>';
                error_log('ShortLinker bulk generation error: ' . $e->getMessage());
            }
        }

        ?>
        <div class="wrap shortlinker-tools-wrap">
            <h1>ShortLinker Tools</h1>

            <div class="shortlinker-info-box">
                <h3>Bulk Generate Shortlinks</h3>
                <p>This tool will scan all published posts and pages and generate shortlinks for any that don't already have them. The shortlinks will be automatically synced with the eanlibya.news redirector.</p>
                <?php
                // Show debug info
                $api_secret = get_option('shortlinker_api_secret', '');
                $api_endpoint = get_option('shortlinker_api_endpoint', $this->api_endpoint);

                if (empty($api_secret)) {
                    echo '<p style="color: red;"><strong>Warning:</strong> API Secret Key is not configured. Go to Settings > ShortLinker to set it up.</p>';
                }

                // Count posts that need shortlinks
                $count_query = new WP_Query(array(
                    'post_type' => array('post', 'page'),
                    'post_status' => 'publish',
                    'posts_per_page' => -1,
                    'fields' => 'ids',
                    'meta_query' => array(
                        array(
                            'key' => $this->meta_key,
                            'compare' => 'NOT EXISTS'
                        )
                    )
                ));

                $posts_needing_shortlinks = $count_query->found_posts;
                wp_reset_postdata();

                echo '<p><strong>Posts needing shortlinks:</strong> ' . $posts_needing_shortlinks . '</p>';
                ?>
            </div>

            <form method="post">
                <?php wp_nonce_field('bulk_generate', 'shortlinker_nonce'); ?>
                <p class="submit">
                    <input type="submit" name="bulk_generate" class="button-primary" value="Bulk Generate Shortlinks" onclick="return confirm('This will generate shortlinks for all posts without them. Continue?');" />
                </p>
            </form>

            <div class="shortlinker-info-box">
                <h3>How It Works</h3>
                <ul>
                    <li>Shortlinks use 4-character alphanumeric codes (e.g., <code>a3Bx</code>)</li>
                    <li>Original URL: <code>https://www.eanlibya.com/post-title</code></li>
                    <li>Short URL: <code>https://eanlibya.news/a3Bx</code></li>
                    <li>Codes are unique and stored in post meta</li>
                    <li>Redirects are synced with eanlibya.news automatically</li>
                </ul>
            </div>
        </div>
        <?php
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        ?>
        <div class="wrap shortlinker-settings-wrap">
            <h1>ShortLinker Settings</h1>

            <div class="shortlinker-info-box">
                <h3>Configuration</h3>
                <p>Configure the connection to your eanlibya.news redirector. Make sure the API secret key matches the one set in your redirector's <code>api/add.php</code> file.</p>
            </div>

            <form method="post" action="options.php">
                <?php
                settings_fields('shortlinker_settings');
                do_settings_sections('shortlinker_settings');
                ?>
                <table class="form-table">
                    <tr>
                        <th scope="row">API Secret Key</th>
                        <td>
                            <input type="password" name="shortlinker_api_secret" value="<?php echo esc_attr(get_option('shortlinker_api_secret', '')); ?>" class="regular-text" />
                            <p class="description">Secret key for authenticating with the redirector API at eanlibya.news. This must match the <code>$API_SECRET</code> value in your redirector's <code>api/add.php</code> file.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Redirector API Endpoint</th>
                        <td>
                            <input type="url" name="shortlinker_api_endpoint" value="<?php echo esc_attr(get_option('shortlinker_api_endpoint', $this->api_endpoint)); ?>" class="regular-text" />
                            <p class="description">URL of the redirector API endpoint (usually <code>https://eanlibya.news/api/add.php</code>)</p>
                        </td>
                    </tr>
                </table>
                <?php submit_button(); ?>
            </form>

            <div class="shortlinker-info-box">
                <h3>Testing Your Configuration</h3>
                <p>After saving your settings, try creating a new post or using the bulk generation tool. Check your server logs and the eanlibya.news redirector to ensure shortlinks are being created successfully.</p>
            </div>
        </div>
        <?php
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        register_setting('shortlinker_settings', 'shortlinker_api_secret');
        register_setting('shortlinker_settings', 'shortlinker_api_endpoint');
    }
    
    /**
     * Bulk generate shortlinks for published posts
     */
    public function bulk_generate_shortlinks() {
        try {
            // First, count how many posts need shortlinks
            $count_query = new WP_Query(array(
                'post_type' => array('post', 'page'),
                'post_status' => 'publish',
                'posts_per_page' => -1,
                'fields' => 'ids',
                'meta_query' => array(
                    array(
                        'key' => $this->meta_key,
                        'compare' => 'NOT EXISTS'
                    )
                )
            ));

            $total_posts = $count_query->found_posts;
            wp_reset_postdata();

            if ($total_posts === 0) {
                return array('generated' => 0, 'skipped' => 0, 'errors' => 0, 'error' => false);
            }

            // Process in batches to avoid memory issues
            $batch_size = 50;
            $generated = 0;
            $skipped = 0;
            $errors = 0;
            $offset = 0;

            while ($offset < $total_posts) {
                $posts = get_posts(array(
                    'post_type' => array('post', 'page'),
                    'post_status' => 'publish',
                    'posts_per_page' => $batch_size,
                    'offset' => $offset,
                    'meta_query' => array(
                        array(
                            'key' => $this->meta_key,
                            'compare' => 'NOT EXISTS'
                        )
                    )
                ));

                if (empty($posts)) {
                    break;
                }

                foreach ($posts as $post) {
                    try {
                        $code = $this->generate_unique_code();
                        if ($code) {
                            update_post_meta($post->ID, $this->meta_key, $code);
                            $sync_result = $this->sync_with_redirector($code, get_permalink($post->ID));
                            if ($sync_result === false) {
                                error_log("ShortLinker: Failed to sync shortlink for post {$post->ID}");
                                // Continue anyway - the shortlink is saved locally
                            }
                            $generated++;
                        } else {
                            $skipped++;
                            error_log("ShortLinker: Could not generate unique code for post {$post->ID}");
                        }
                    } catch (Exception $e) {
                        $errors++;
                        error_log("ShortLinker: Error processing post {$post->ID}: " . $e->getMessage());
                    }
                }

                $offset += $batch_size;

                // Clear memory
                wp_reset_postdata();

                // Small delay to prevent overwhelming the server
                usleep(100000); // 0.1 seconds
            }

            return array(
                'generated' => $generated,
                'skipped' => $skipped,
                'errors' => $errors,
                'error' => false
            );

        } catch (Exception $e) {
            error_log('ShortLinker bulk generation critical error: ' . $e->getMessage());
            return array(
                'generated' => 0,
                'skipped' => 0,
                'errors' => 0,
                'error' => 'Critical error: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Sync shortlink with external redirector
     */
    public function sync_with_redirector($code, $url) {
        $api_secret = get_option('shortlinker_api_secret', '');
        $api_endpoint = get_option('shortlinker_api_endpoint', $this->api_endpoint);

        if (empty($api_secret)) {
            error_log('ShortLinker: API secret not configured');
            return false;
        }

        if (empty($api_endpoint)) {
            error_log('ShortLinker: API endpoint not configured');
            return false;
        }

        $data = array(
            'code' => $code,
            'url' => $url
        );

        try {
            $response = wp_remote_post($api_endpoint, array(
                'headers' => array(
                    'Content-Type' => 'application/json',
                    'X-Shortlink-Key' => $api_secret
                ),
                'body' => json_encode($data),
                'timeout' => 15, // Reduced timeout for bulk operations
                'blocking' => true
            ));

            if (is_wp_error($response)) {
                error_log('ShortLinker: API request failed - ' . $response->get_error_message());
                return false;
            }

            $response_code = wp_remote_retrieve_response_code($response);
            $response_body = wp_remote_retrieve_body($response);

            if ($response_code !== 200) {
                error_log("ShortLinker: API returned error code {$response_code}: {$response_body}");
                return false;
            }

            // Log successful sync (optional - can be disabled for performance)
            // error_log("ShortLinker: Successfully synced {$code} -> {$url}");

            return true;

        } catch (Exception $e) {
            error_log('ShortLinker: Exception during API sync - ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Copy shortlink shortcode
     */
    public function copy_shortlink_shortcode($atts) {
        if (!is_single() && !is_page()) {
            return '';
        }
        
        global $post;
        $short_code = get_post_meta($post->ID, $this->meta_key, true);
        
        if (!$short_code) {
            return '';
        }
        
        $short_url = $this->short_domain . '/' . $short_code;
        
        return '<button class="shortlinker-copy-btn button" data-url="' . esc_attr($short_url) . '">Copy Short Link</button>';
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if (in_array($hook, array('post.php', 'post-new.php', 'tools_page_shortlinker-tools', 'settings_page_shortlinker-settings'))) {
            wp_enqueue_script('shortlinker-admin', SHORTLINKER_PLUGIN_URL . 'js/admin.js', array('jquery'), SHORTLINKER_VERSION, true);
            wp_enqueue_style('shortlinker-admin', SHORTLINKER_PLUGIN_URL . 'css/admin.css', array(), SHORTLINKER_VERSION);
        }
    }
    
    /**
     * Enqueue frontend scripts
     */
    public function enqueue_frontend_scripts() {
        if (is_single() || is_page()) {
            wp_enqueue_script('shortlinker-frontend', SHORTLINKER_PLUGIN_URL . 'js/frontend.js', array('jquery'), SHORTLINKER_VERSION, true);
        }
    }
}

// Initialize the plugin
new ShortLinker();
