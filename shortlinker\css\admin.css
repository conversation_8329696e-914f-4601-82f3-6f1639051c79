/* ShortLinker Admin Styles */

.shortlinker-copy-btn {
    background-color: #0073aa;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 13px;
    transition: background-color 0.3s ease;
    margin-top: 5px;
}

.shortlinker-copy-btn:hover {
    background-color: #005a87;
    color: white;
}

.shortlinker-copy-btn:focus {
    outline: 1px solid #005a87;
    outline-offset: 2px;
}

.shortlinker-copy-btn.copied {
    background-color: #46b450;
}

#shortlinker_url {
    font-family: monospace;
    font-size: 12px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    padding: 5px;
}

/* Meta box styling */
#shortlinker_meta_box .inside {
    margin: 0;
    padding: 12px;
}

#shortlinker_meta_box p {
    margin: 0 0 10px 0;
}

#shortlinker_meta_box label {
    font-weight: 600;
    display: block;
    margin-bottom: 5px;
}

/* Admin tools page */
.shortlinker-tools-wrap {
    max-width: 800px;
}

.shortlinker-tools-wrap .notice {
    margin: 20px 0;
}

.shortlinker-info-box {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-left: 4px solid #0073aa;
    padding: 12px;
    margin: 20px 0;
}

.shortlinker-info-box h3 {
    margin-top: 0;
}

/* Settings page */
.shortlinker-settings-wrap .form-table th {
    width: 200px;
}

.shortlinker-settings-wrap .form-table td input[type="text"],
.shortlinker-settings-wrap .form-table td input[type="url"],
.shortlinker-settings-wrap .form-table td input[type="password"] {
    width: 400px;
}

.shortlinker-settings-wrap .description {
    font-style: italic;
    color: #666;
}
